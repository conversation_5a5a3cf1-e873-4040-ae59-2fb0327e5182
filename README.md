# Fund Strategy Backtest Calculator

基于 Next.js 和 React 19 构建的基金投资策略回测分析工具，支持真实基金数据获取和多种投资策略分析。

## 🚀 功能特性

### ✅ 已完成功能

- ✨ **真实数据支持**: 集成百度股市通API和CSRC API，获取真实基金净值数据
- 🔍 **智能基金搜索**: 支持基金代码和名称搜索，实时验证基金有效性
- 📊 **指数数据对比**: 集成CSIndex API，支持指数数据获取和对比分析
- 🎯 **多种投资策略**: 定投、价值平均、智能定投、网格交易、动量策略、均值回归
- 📈 **收益率对比分析**: 使用recharts展示策略、基金、指数收益率对比
- 🎨 **现代化UI**: 基于Tailwind CSS + Radix UI的响应式设计
- 📱 **交互式图表**: 悬停提示、响应式设计、专业级可视化
- 📝 **表单验证**: 使用React Hook Form进行表单管理和验证
- 🗓️ **日期处理**: 使用date-fns进行日期格式化和计算
- 💰 **精确计算**: 使用Decimal.js进行高精度数值计算
- 🧪 **单元测试**: 使用Vitest进行全面的单元测试覆盖

### 🚧 开发中功能

- 🔄 **Server Actions**: 正在将更多API调用迁移到Server Actions
- 🎛️ **高级UI组件**: 正在集成更多Radix UI组件
- 📊 **数据可视化增强**: 计划添加更多图表类型和交互功能

### ❌ 已移除功能

- 🗑️ **Mock数据**: 已完全移除mock数据，全部使用真实API
- 🗑️ **冗余组件**: 移除了未使用的组件文件
- 🗑️ **API路由**: 部分API路由已迁移到Server Actions

## 技术栈

### 核心框架
- **前端框架**: Next.js 15.3.3 (App Router + Server Components)
- **UI 库**: React 19.1.0
- **类型检查**: TypeScript 5.8.3

### UI 和样式
- **样式框架**: Tailwind CSS 4
- **UI 组件**: Radix UI (Dialog, Select, Tooltip, Switch)
- **图表库**: Recharts 2.15.3

### 表单和数据处理
- **表单管理**: React Hook Form 7.57.0
- **日期处理**: date-fns 4.1.0
- **数值计算**: Decimal.js 10.5.0

### 开发工具
- **代码质量**: ESLint + Prettier
- **测试框架**: Vitest
- **依赖分析**: Knip

## 快速开始

### 环境要求

- Node.js 18+
- pnpm (推荐) 或 npm

### 安装依赖

```bash
pnpm install
```

### 配置环境变量

复制环境变量示例文件：

```bash
cp .env.local.example .env.local
```

编辑 `.env.local` 文件：

```bash
# 开发配置
NEXT_PUBLIC_DEBUG=true
```

### 启动开发服务器

```bash
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
pnpm build
pnpm start
```

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API路由
│   │   └── fund/          # 基金数据API
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页面
├── components/            # React 组件
│   ├── BacktestChart.tsx      # 回测结果图表
│   ├── FundSelector.tsx       # 基金选择器
│   ├── FundSearch.tsx         # 基金搜索组件
│   ├── ParameterInputs.tsx    # 参数输入组件
│   └── StrategySelector.tsx   # 策略选择器
├── lib/                   # 工具库和业务逻辑
│   ├── api/              # API服务层
│   │   ├── fund-api.ts    # 基金数据API服务
│   │   ├── types.ts      # API类型定义
│   │   └── utils.ts      # API工具函数
│   ├── backtest.ts       # 回测引擎
│   ├── config.ts         # 应用配置
│   ├── mock-data.ts      # 数据获取服务
│   ├── strategies.ts     # 投资策略定义
│   └── utils.ts          # 工具函数
└── types/                # TypeScript 类型定义
    └── fund.ts           # 基金相关类型
```

## 📊 基金数据获取

### 支持的基金代码格式

- 6位数字基金代码（如：000628）
- 支持大部分公募基金

### API功能

- **基金净值数据**: 获取指定时间范围的历史净值数据
- **业绩对比分析**: 获取基金与同类平均、指数的收益率对比
- **数据验证**: 实时验证基金代码有效性
- **缓存机制**: 自动缓存数据，减少API调用
- **错误重试**: 网络异常时自动重试
- **性能监控**: 内置API性能测试工具

### 数据源特点

- **数据完整性**: 100%，无缺失数据
- **更新频率**: T+1日更新
- **响应速度**: 平均150ms
- **数据精度**: 净值4位小数
- **历史深度**: 最长支持124个月

### 使用示例

```typescript
import {
  fetchFundData,
  getPerformanceComparison,
  getFundBasicInfo,
} from "@/lib/api/fund-api";

// 获取基金历史净值数据
const data = await fetchFundData("000628", "2024-01-01", "2024-12-31");

// 获取业绩对比数据
const comparison = await getPerformanceComparison("000628", 12);

// 获取基金基本信息
const info = await getFundBasicInfo("000628");
```

### API测试工具

```bash
# 运行API接口测试
npm run test:api

# 或者使用tsx直接运行
npx tsx scripts/test-api.ts
```

## 🎯 投资策略

### 1. 定投策略 (Fixed Amount)

- 固定金额定期投资
- 支持周投、月投、季投
- 适合长期稳健投资

### 2. 价值平均策略 (Value Averaging)

- 根据目标增长率调整投资金额
- 自动低买高卖
- 适合有经验的投资者

### 3. 智能定投 (Smart Fixed)

- 基于估值指标调整投资金额
- 支持PE、PB、RSI等指标
- 提高投资效率

### 4. 网格交易 (Grid Trading)

- 在价格区间内设置买卖网格
- 适合震荡市场
- 需要设定合理的价格区间

### 5. 动量策略 (Momentum)

- 基于价格趋势进行投资
- 追涨杀跌策略
- 适合趋势明显的市场

### 6. 均值回归 (Mean Reversion)

- 基于移动平均线偏离度
- 逆向投资策略
- 适合波动较大的市场

## 📈 收益率对比分析

### 可视化图表功能

使用recharts库实现的专业级收益率对比图表，包含：

- **三线对比图表**：

  - 🔵 策略收益率（蓝色实线）：投资策略的累计收益率
  - 🟢 基金收益率（绿色实线）：基金净值的累计收益率（买入并持有）
  - 🟣 指数收益率（紫色虚线）：基准指数的累计收益率

- **交互式功能**：

  - 悬停提示显示具体数值
  - 响应式设计适配各种屏幕
  - 零轴参考线清晰显示盈亏分界

- **超额收益分析**：
  - 相对基金超额收益 = 策略收益率 - 基金收益率
  - 相对指数超额收益 = 策略收益率 - 指数收益率
  - 颜色编码：绿色表示正超额收益，红色表示负超额收益

### 图表解读指南

1. **策略表现评估**：

   - 蓝线在绿线上方 → 策略优于买入持有
   - 蓝线在紫线上方 → 策略跑赢市场基准
   - 线条波动幅度 → 反映策略风险水平

2. **投资决策参考**：
   - 持续正超额收益 → 策略有效性较高
   - 收益率曲线平滑 → 策略稳定性较好
   - 回撤控制情况 → 风险管理能力

## 📊 风险指标

- **总收益率**: 投资期间的总收益百分比
- **年化收益率**: 按年计算的平均收益率
- **波动率**: 收益率的标准差，衡量风险
- **夏普比率**: 风险调整后的收益指标
- **最大回撤**: 投资期间的最大亏损幅度

## 🛠️ 开发指南

### 代码规范

项目使用 ESLint 和 Prettier 进行代码规范检查：

```bash
# 检查代码规范
pnpm lint

# 自动修复代码规范问题
pnpm lint:fix

# 格式化代码
pnpm format
```

### 添加新的投资策略

1. 在 `src/types/fund.ts` 中定义策略参数类型
2. 在 `src/lib/strategies.ts` 中添加策略配置
3. 在 `src/lib/backtest.ts` 中实现策略计算逻辑

### 添加新的数据源

1. 在 `src/lib/api/` 目录下创建新的API服务
2. 实现数据获取和转换逻辑
3. 在配置文件中添加数据源选项

## 🧪 测试功能

项目使用 Vitest 进行单元测试：

```bash
# 运行测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 运行测试的交互式界面
pnpm test:ui
```

## 🚀 部署

### Vercel 部署

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 设置环境变量
4. 自动部署完成

### 环境变量配置

在部署平台设置以下环境变量：

```bash
NEXT_PUBLIC_DEBUG=false
```

### 其他平台

项目是标准的 Next.js 应用，可以部署到任何支持 Node.js 的平台。

## 🔍 故障排除

### 常见问题

1. **CORS错误**: 如果遇到跨域问题，可以使用Next.js API路由作为代理
2. **API限制**: 百度API可能有调用频率限制，建议启用缓存
3. **基金代码无效**: 确保使用6位数字的标准基金代码

### 调试模式

设置 `NEXT_PUBLIC_DEBUG=true` 启用调试模式，查看详细的API调用日志。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请创建 Issue 或联系项目维护者。

---

**注意**: 使用真实基金数据时，请遵守相关API的使用条款和限制。本工具仅供学习和研究使用，不构成投资建议。

"use client";

import { useState } from "react";

import type { Fund } from "@/types/fund";

import FundSearch from "./fund-search";

const getFundTypeLabel = (type: Fund["type"]) => {
  const typeLabels = {
    stock: "股票型",
    bond: "债券型",
    hybrid: "混合型",
    index: "指数型",
    money: "货币型",
  };
  return typeLabels[type];
};

const getFundTypeColor = (type: Fund["type"]) => {
  const typeColors = {
    stock: "bg-red-100 text-red-800",
    bond: "bg-green-100 text-green-800",
    hybrid: "bg-blue-100 text-blue-800",
    index: "bg-purple-100 text-purple-800",
    money: "bg-yellow-100 text-yellow-800",
  };
  return typeColors[type];
};

interface FundSelectorProperties {
  selectedFund: Fund | null;
  onFundSelect: (fund: Fund) => void;
}

export default function FundSelector({
  selectedFund,
  onFundSelect,
}: FundSelectorProperties) {

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold text-gray-900">选择基金</h3>
        <p className="text-sm text-gray-600 mt-1">
          输入基金代码或名称进行搜索
        </p>
      </div>

      {/* 基金搜索组件 */}
      <FundSearch
        onFundSelect={onFundSelect}
        selectedFund={selectedFund || undefined}
        placeholder="输入基金代码或名称进行搜索..."
        className="w-full"
      />

      {/* 选中的基金信息 */}
      {selectedFund ? (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">已选择基金</h4>
          <div className="text-sm text-blue-800">
            <p>
              <span className="font-medium">名称:</span> {selectedFund.name}
            </p>
            <p>
              <span className="font-medium">代码:</span> {selectedFund.code}
            </p>
            <p>
              <span className="font-medium">类型:</span>{" "}
              {getFundTypeLabel(selectedFund.type)}
            </p>
            {selectedFund.description ? (
              <p>
                <span className="font-medium">描述:</span>{" "}
                {selectedFund.description}
              </p>
            ) : null}
          </div>
        </div>
      ) : null}
    </div>
  );
}

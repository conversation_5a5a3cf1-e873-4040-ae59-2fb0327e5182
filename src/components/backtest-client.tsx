"use client";

import { useState } from "react";

import BacktestChart from "@/components/backtest-chart";
import FundSelector from "@/components/fund-selector";
import IndexSelector from "@/components/index-selector";
import ParameterInputs from "@/components/parameter-inputs";
import StrategySelector from "@/components/strategy-selector";
import { fetchFundDataAction, fetchIndexDataAction } from "@/lib/actions";
import { createBacktestEngine, runBacktest } from "@/lib/backtest";
import type {
  Fund,
  Strategy,
  StrategyParameters,
  BacktestResult,
} from "@/types/fund";

interface IndexType {
  id: string;
  name: string;
  code: string;
  description?: string;
}

export default function BacktestClient() {
  const [activeTab, setActiveTab] = useState<"backtest" | "grid">("backtest");
  const [selectedFund, setSelectedFund] = useState<Fund | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<IndexType | null>(null);
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(
    null
  );
  const [parameters, setParameters] = useState<Record<string, unknown>>({});
  const [backtestResult, setBacktestResult] = useState<BacktestResult | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 执行回测
  const handleRunBacktest = async () => {
    if (!selectedFund || !selectedStrategy) {
      setError("请选择基金和投资策略");
      return;
    }

    // 验证必填参数
    const requiredParameters = Object.entries(selectedStrategy.parameterSchema)
      .filter(([, parameter]) => parameter.required)
      .map(([key]) => key);

    const missingParameters = requiredParameters.filter(
      (key) => !parameters[key] || parameters[key] === ""
    );
    if (missingParameters.length > 0) {
      setError("请填写所有必填参数");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 获取基金数据 - 使用基金代码而不是ID
      const fundData = await fetchFundDataAction(
        selectedFund.code,
        parameters.startDate as string,
        parameters.endDate as string
      );

      // 获取指数数据（如果选择了指数）
      let indexData;
      if (selectedIndex) {
        indexData = await fetchIndexDataAction(
          selectedIndex.code,
          parameters.startDate as string,
          parameters.endDate as string
        );
      }

      // 创建回测引擎配置并执行回测
      const engineConfig = createBacktestEngine(fundData, indexData);
      const result = await runBacktest(
        engineConfig,
        selectedFund,
        parameters as StrategyParameters
      );

      setBacktestResult(result);
    } catch (error_) {
      console.error("回测执行失败:", error_);
      setError("回测执行失败，请检查参数设置");
    } finally {
      setIsLoading(false);
    }
  };

  // 重置所有状态
  const resetAll = () => {
    setSelectedFund(null);
    setSelectedStrategy(null);
    setParameters({});
    setBacktestResult(null);
    setError(null);
  };

  return (
    <>
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                基金投资策略回测计算器
              </h1>
              <p className="mt-2 text-gray-600">
                选择基金和投资策略，分析历史表现
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {/* 标签页切换 */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => {
                    setActiveTab("backtest");
                  }}
                  className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeTab === "backtest"
                      ? "bg-white text-gray-900 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  回测分析
                </button>
                <button
                  onClick={() => {
                    setActiveTab("grid");
                  }}
                  className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeTab === "grid"
                      ? "bg-white text-gray-900 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  网格交易
                </button>
              </div>

              <button
                onClick={resetAll}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                重置
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === "grid" ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="text-center py-12">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                网格交易功能
              </h3>
              <p className="text-gray-500">
                网格交易功能正在开发中，敬请期待...
              </p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 左侧配置面板 */}
            <div className="lg:col-span-1 space-y-6">
              {/* 基金选择 */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <FundSelector
                  selectedFund={selectedFund}
                  onFundSelect={setSelectedFund}
                />
              </div>

              {/* 指数选择 */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <IndexSelector
                  selectedIndex={selectedIndex}
                  onIndexSelect={setSelectedIndex}
                />
              </div>

              {/* 策略选择 */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <StrategySelector
                  selectedStrategy={selectedStrategy}
                  onStrategySelect={setSelectedStrategy}
                />
              </div>

              {/* 参数设置 */}
              {selectedStrategy ? (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <ParameterInputs
                    strategy={selectedStrategy}
                    parameters={parameters}
                    onParametersChange={setParameters}
                  />
                </div>
              ) : null}

              {/* 执行回测按钮 */}
              {selectedFund && selectedStrategy ? (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <button
                    onClick={handleRunBacktest}
                    disabled={isLoading}
                    className={`w-full py-3 px-4 rounded-lg font-medium text-white transition-colors ${
                      isLoading
                        ? "bg-gray-400 cursor-not-allowed"
                        : "bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    }`}
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <svg
                          className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          />
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          />
                        </svg>
                        执行回测中...
                      </div>
                    ) : (
                      "开始回测"
                    )}
                  </button>

                  {error ? (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center">
                        <svg
                          className="w-5 h-5 text-red-400 mr-2"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-sm text-red-800">{error}</span>
                      </div>
                    </div>
                  ) : null}
                </div>
              ) : null}
            </div>

            {/* 右侧结果展示 */}
            <div className="lg:col-span-2">
              {backtestResult ? (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <BacktestChart result={backtestResult} />
                </div>
              ) : (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="text-center py-12">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      暂无回测结果
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      请选择基金和投资策略，设置参数后开始回测
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </main>
    </>
  );
}

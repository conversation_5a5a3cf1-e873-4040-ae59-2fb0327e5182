import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, vi, beforeEach } from "vitest";

import BacktestClient from "@/components/backtest-client";
import { fetchFundData } from "@/lib/api/fund-api";
import { fetchIndexData } from "@/lib/api/index-api";
import { createBacktestEngine, runBacktest } from "@/lib/backtest";
import type { Fund } from "@/types/fund";

// Mock dependencies
vi.mock("@/lib/backtest");
vi.mock("@/lib/api/fund-api");
vi.mock("@/lib/api/index-api");

const mockFunds: Fund[] = [
  {
    id: "000001",
    name: "测试基金A",
    code: "000001",
    type: "stock",
    riskLevel: "high",
    description: "测试基金A描述",
    indexId: "CSI300",
  },
  {
    id: "000002",
    name: "测试基金B",
    code: "000002",
    type: "bond",
    riskLevel: "low",
    description: "测试基金B描述",
  },
];

const mockFundData = [
  { date: "2024-01-01", netAssetValue: 1, accumulatedValue: 1 },
  { date: "2024-01-02", netAssetValue: 1.01, accumulatedValue: 1.01 },
];

const mockBacktestResult = {
  strategy: "fixed_amount" as const,
  fund: mockFunds[0],
  params: {
    startDate: "2024-01-01",
    endDate: "2024-01-31",
    initialAmount: 1000,
    monthlyAmount: 500,
    frequency: "monthly" as const,
  },
  performance: {
    totalInvestment: 1500,
    finalValue: 1520,
    totalReturn: 1.33,
    annualizedReturn: 16,
    maxDrawdown: -2.5,
    volatility: 15.2,
    sharpeRatio: 1.05,
  },
  timeline: [
    {
      date: "2024-01-01",
      investment: 1000,
      shares: 1000,
      value: 1000,
      totalInvestment: 1000,
      netAssetValue: 1,
    },
  ],
};

describe("BacktestClient", () => {
  const mockRunBacktest = vi.fn();
  const mockCreateBacktestEngine = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock backtest functions
    mockCreateBacktestEngine.mockReturnValue({ fundData: mockFundData, indexData: [] });
    (createBacktestEngine as any).mockImplementation(mockCreateBacktestEngine);
    (runBacktest as any).mockImplementation(mockRunBacktest);

    // Mock data functions
    (fetchFundData as any).mockResolvedValue(mockFundData);
    (fetchIndexData as any).mockResolvedValue([]);

    mockRunBacktest.mockResolvedValue(mockBacktestResult);
  });

  it("应该渲染所有主要组件", () => {
    render(<BacktestClient initialFunds={mockFunds} />);

    expect(screen.getByText("基金投资策略回测系统")).toBeInTheDocument();
    expect(screen.getByText("回测分析")).toBeInTheDocument();
    expect(screen.getByText("数据测试")).toBeInTheDocument();
    expect(screen.getByText("网格交易")).toBeInTheDocument();
    expect(screen.getByText("收益对比")).toBeInTheDocument();
  });

  it("应该显示基金选择器", () => {
    render(<BacktestClient initialFunds={mockFunds} />);

    expect(screen.getByText("选择基金")).toBeInTheDocument();
    expect(screen.getByText("测试基金A")).toBeInTheDocument();
    expect(screen.getByText("测试基金B")).toBeInTheDocument();
  });

  it("应该显示策略选择器", () => {
    render(<BacktestClient initialFunds={mockFunds} />);

    expect(screen.getByText("选择投资策略")).toBeInTheDocument();
  });

  it("应该处理标签页切换", async () => {
    const user = userEvent.setup();
    render(<BacktestClient initialFunds={mockFunds} />);

    // 切换到数据测试标签
    await user.click(screen.getByText("数据测试"));
    expect(screen.getByText("基金数据测试")).toBeInTheDocument();

    // 切换到网格交易标签
    await user.click(screen.getByText("网格交易"));
    expect(screen.getByText("网格交易策略演示")).toBeInTheDocument();

    // 切换到收益对比标签
    await user.click(screen.getByText("收益对比"));
    expect(screen.getByText("基金收益率对比分析")).toBeInTheDocument();

    // 切换回回测分析标签
    await user.click(screen.getByText("回测分析"));
    expect(screen.getByText("选择基金")).toBeInTheDocument();
  });

  it("应该处理基金选择", async () => {
    const user = userEvent.setup();
    render(<BacktestClient initialFunds={mockFunds} />);

    // 选择基金
    await user.click(screen.getByText("测试基金A"));

    await waitFor(() => {
      expect(screen.getByText("已选择基金")).toBeInTheDocument();
    });
  });

  it("应该执行回测", async () => {
    const user = userEvent.setup();
    render(<BacktestClient initialFunds={mockFunds} />);

    // 选择基金
    await user.click(screen.getByText("测试基金A"));

    // 选择策略
    await user.click(screen.getByText("定期定额投资"));

    // 等待参数输入组件出现并填写参数
    await waitFor(() => {
      expect(screen.getByText("投资参数设置")).toBeInTheDocument();
    });

    // 点击开始回测按钮
    const backtestButton = screen.getByText("开始回测");
    await user.click(backtestButton);

    await waitFor(() => {
      expect(fetchFundData).toHaveBeenCalled();
      expect(mockRunBacktest).toHaveBeenCalled();
    });
  });

  it("应该处理回测错误", async () => {
    const user = userEvent.setup();
    mockRunBacktest.mockRejectedValue(new Error("回测失败"));

    render(<BacktestClient initialFunds={mockFunds} />);

    // 选择基金和策略
    await user.click(screen.getByText("测试基金A"));
    await user.click(screen.getByText("定期定额投资"));

    // 开始回测
    await waitFor(() => {
      expect(screen.getByText("开始回测")).toBeInTheDocument();
    });

    await user.click(screen.getByText("开始回测"));

    await waitFor(() => {
      expect(
        screen.getByText("回测执行失败，请检查参数设置")
      ).toBeInTheDocument();
    });
  });

  it("应该显示加载状态", async () => {
    const user = userEvent.setup();
    // 让回测函数延迟返回
    mockRunBacktest.mockImplementation(
      () => new Promise((resolve) => setTimeout(resolve, 100))
    );

    render(<BacktestClient initialFunds={mockFunds} />);

    // 选择基金和策略
    await user.click(screen.getByText("测试基金A"));
    await user.click(screen.getByText("定期定额投资"));

    // 开始回测
    await waitFor(() => {
      expect(screen.getByText("开始回测")).toBeInTheDocument();
    });

    await user.click(screen.getByText("开始回测"));

    // 应该显示加载状态
    expect(screen.getByText("回测中...")).toBeInTheDocument();
  });

  it("应该支持重置功能", async () => {
    const user = userEvent.setup();
    render(<BacktestClient initialFunds={mockFunds} />);

    // 选择基金
    await user.click(screen.getByText("测试基金A"));

    await waitFor(() => {
      expect(screen.getByText("已选择基金")).toBeInTheDocument();
    });

    // 重置
    const resetButton = screen.getByText("重置");
    await user.click(resetButton);

    // 验证状态已重置
    expect(screen.queryByText("已选择基金")).not.toBeInTheDocument();
  });

  it("应该处理没有基金数据的情况", () => {
    render(<BacktestClient initialFunds={[]} />);

    expect(screen.getByText("选择基金")).toBeInTheDocument();
    // 应该显示空状态或提示信息
  });

  it("应该处理指数数据获取失败", async () => {
    const user = userEvent.setup();
    (fetchIndexData as any).mockRejectedValue(new Error("指数数据获取失败"));

    render(<BacktestClient initialFunds={mockFunds} />);

    // 选择有指数关联的基金
    await user.click(screen.getByText("测试基金A"));
    await user.click(screen.getByText("定期定额投资"));

    await waitFor(() => {
      expect(screen.getByText("开始回测")).toBeInTheDocument();
    });

    await user.click(screen.getByText("开始回测"));

    // 即使指数数据获取失败，回测也应该继续
    await waitFor(() => {
      expect(mockRunBacktest).toHaveBeenCalled();
    });
  });
});

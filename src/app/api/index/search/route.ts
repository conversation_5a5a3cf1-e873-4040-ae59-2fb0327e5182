import { NextRequest, NextResponse } from "next/server";

interface CSIndexSearchRequest {
  sorter: {
    sortField: string | null;
    sortOrder: string | null;
  };
  pager: {
    pageNum: number;
    pageSize: number;
  };
  searchInput: string;
  indexFilter: {
    ifCustomized: null;
    ifTracked: null;
    ifWeightCapped: null;
    indexCompliance: null;
    hotSpot: null;
    indexClassify: null;
    currency: null;
    region: null;
    indexSeries: null;
    undefined: null;
  };
}

interface CSIndexItem {
  indexCode: string;
  indexName: string;
  indexNameEn: string;
  publishDate: string;
  baseDate: string;
  basePoint: number;
  indexType: string;
  indexClassify: string;
  currency: string;
  region: string;
}

interface CSIndexApiResponse {
  code: string;
  message: string;
  data: {
    data: CSIndexItem[];
    total: number;
    pageNum: number;
    pageSize: number;
  };
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");
    const pageNum = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("size") || "10");

    if (!query) {
      return NextResponse.json(
        { success: false, error: "搜索关键词不能为空" },
        { status: 400 }
      );
    }

    console.log(`指数搜索请求: query=${query}, pageNum=${pageNum}, pageSize=${pageSize}`);

    // 构建CSIndex API请求体
    const requestBody: CSIndexSearchRequest = {
      sorter: {
        sortField: null,
        sortOrder: null,
      },
      pager: {
        pageNum,
        pageSize,
      },
      searchInput: query,
      indexFilter: {
        ifCustomized: null,
        ifTracked: null,
        ifWeightCapped: null,
        indexCompliance: null,
        hotSpot: null,
        indexClassify: null,
        currency: null,
        region: null,
        indexSeries: null,
        undefined: null,
      },
    };

    console.log("CSIndex API请求体:", JSON.stringify(requestBody, null, 2));

    const response = await fetch(
      "https://www.csindex.com.cn/csindex-home/index-list/query-index-item",
      {
        method: "POST",
        headers: {
          Accept: "application/json, text/plain, */*",
          "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
          "Content-Type": "application/json;charset=UTF-8",
          Origin: "https://www.csindex.com.cn",
          Pragma: "no-cache",
          Referer: "https://www.csindex.com.cn/csindex-home/index-list",
          "User-Agent":
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
        },
        body: JSON.stringify(requestBody),
      }
    );

    console.log(`CSIndex API响应状态: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("CSIndex API错误响应:", errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data: any = await response.json();
    console.log("CSIndex API响应数据:", JSON.stringify(data, null, 2));

    // 检查响应格式并处理不同的情况
    if (!data) {
      throw new Error("API返回空数据");
    }

    // 处理不同的响应格式
    let indices: any[] = [];
    let total = 0;
    let actualPageNum = pageNum;
    let actualPageSize = pageSize;

    if (data.success === false || data.code === "500") {
      // API返回错误
      throw new Error(data.msg || data.message || "API服务异常");
    }

    if (data.code === "200" && data.data) {
      // 标准成功响应
      if (Array.isArray(data.data)) {
        indices = data.data.map((item: any) => ({
          id: item.indexCode,
          name: item.indexName,
          code: item.indexCode,
          description: `${item.indexClassify || "未分类"} | 发布日期: ${item.publishDate || "未知"} | 最新收盘: ${item.latestClose || "未知"}`,
          type: item.indexType || item.assetsClassify || "股票指数",
          classify: item.indexClassify || "未分类",
          currency: item.currency || "人民币",
          region: item.region || "境内",
          publishDate: item.publishDate || "",
          baseDate: "", // CSIndex API没有baseDate字段
          basePoint: 0, // CSIndex API没有basePoint字段
          latestClose: item.latestClose || "",
          monthlyReturn: item.monthlyReturn || "",
        }));
        total = data.total || indices.length;
        actualPageNum = data.currentPage || pageNum;
        actualPageSize = data.pageSize || pageSize;
      } else {
        console.warn("API响应中data.data不是数组:", data.data);
      }
    } else if (Array.isArray(data)) {
      // 直接返回数组的情况
      indices = data.map((item: CSIndexItem) => ({
        id: item.indexCode,
        name: item.indexName,
        code: item.indexCode,
        description: `${item.indexClassify || "未分类"} | 基准日期: ${item.baseDate || "未知"} | 基准点数: ${item.basePoint || "未知"}`,
        type: item.indexType || "未知",
        classify: item.indexClassify || "未分类",
        currency: item.currency || "CNY",
        region: item.region || "中国",
        publishDate: item.publishDate || "",
        baseDate: item.baseDate || "",
        basePoint: item.basePoint || 0,
      }));
      total = indices.length;
    } else {
      console.warn("未知的API响应格式:", data);
      throw new Error("API响应格式不正确");
    }

    console.log(`成功解析指数数据: ${indices.length} 条记录`);

    return NextResponse.json({
      success: true,
      data: indices,
      total,
      page: actualPageNum,
      size: actualPageSize,
    });
  } catch (error) {
    console.error("指数搜索失败:", error);
    const errorMessage = error instanceof Error ? error.message : "指数搜索失败";
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}

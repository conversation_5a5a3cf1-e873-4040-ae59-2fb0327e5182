// 基金数据API服务

import type { FundData } from "@/types/fund";

import type { BaiduApiResponse, DataFetchOptions } from "./types";
import {
  parseFundData,
  buildApiUrl,
  validateFundCode as validateFundCodeFormat,
  createApiError,
  getCacheItem,
  setCacheItem,
  withRetry,
  withTimeout,
  generateCacheKey,
  validateDateRange,
  formatErrorMessage,
} from "./utilities";

/**
 * 基金API服务函数
 */

// 默认配置
const defaultOptions: Required<DataFetchOptions> = {
  useCache: true,
  timeout: 10_000,
  retryCount: 3,
  retryDelay: 1000,
};

/**
 * 获取基金历史净值数据
 */
async function getFundData(
  fundCode: string,
  startDate?: string,
  endDate?: string,
  options: DataFetchOptions = {}
): Promise<FundData[]> {
  const options_ = { ...defaultOptions, ...options };

  // 验证基金代码
  if (!validateFundCodeFormat(fundCode)) {
    throw createApiError("INVALID_FUND_CODE", `无效的基金代码: ${fundCode}`);
  }

  // 验证日期范围
  if (startDate && endDate && !validateDateRange(startDate, endDate)) {
    throw createApiError("INVALID_DATE_RANGE", "开始日期不能晚于结束日期");
  }

  // 检查缓存
  const cacheKey = generateCacheKey(fundCode, startDate, endDate);
  if (options_.useCache) {
    const cachedData = getCacheItem<FundData[]>(cacheKey);
    if (cachedData) {
      return cachedData;
    }
  }

  try {
    // 获取原始数据
    let fundData = await fetchRawData(fundCode, startDate, endDate, options_);

    // 应用日期过滤
    if (startDate || endDate) {
      fundData = filterByDateRange(fundData, startDate, endDate);
    }

    // 缓存结果
    if (options_.useCache) {
      setCacheItem(cacheKey, fundData);
    }

    return fundData;
  } catch (error) {
    const errorMessage = formatErrorMessage(error);
    throw createApiError(
      "API_ERROR",
      `获取基金数据失败: ${errorMessage}`,
      error
    );
  }
}

/**
 * 获取基金基本信息 (服务端专用)
 */
export async function getFundInfo(
  fundCode: string
): Promise<{ code: string; name: string } | null> {
  if (!validateFundCodeFormat(fundCode)) {
    return null;
  }

  try {
    // 直接调用外部API
    const url = buildApiUrl(fundCode);
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
        Accept: "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache",
        Pragma: "no-cache",
      },
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    if (data.ResultCode !== "0") {
      return null;
    }

    // 从API响应中提取基金名称
    const result = data.Result?.[0];
    const query = result?.DisplayData?.resultData?.extData?.OriginQuery;

    if (query === fundCode) {
      return {
        code: fundCode,
        name: `基金${fundCode}`, // 实际应该从API响应中提取
      };
    }

    return null;
  } catch (error) {
    console.warn(`获取基金${fundCode}信息失败:`, error);
    return null;
  }
}

/**
 * 验证基金代码是否存在
 */
async function validateFundCode(fundCode: string): Promise<boolean> {
  try {
    const info = await getFundInfo(fundCode);
    return info !== null;
  } catch {
    return false;
  }
}

/**
 * 获取原始API数据 (服务端专用)
 */
async function fetchRawData(
  fundCode: string,
  startDate?: string,
  endDate?: string,
  options: Required<DataFetchOptions> = defaultOptions
): Promise<FundData[]> {
  // 直接调用外部API
  const url = buildApiUrl(fundCode);

  const fetchWithRetry = () =>
    withRetry(
      async () => {
        const response = await fetch(url, {
          method: "GET",
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            Accept: "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Cache-Control": "no-cache",
            Pragma: "no-cache",
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.ResultCode !== "0") {
          throw new Error(`API错误: ${data.ResultCode}`);
        }

        return data as BaiduApiResponse;
      },
      { maxRetries: options.retryCount, delay: options.retryDelay }
    );

  const rawData = await withTimeout(fetchWithRetry(), options.timeout);
  const parsedData = parseFundData(rawData);

  // 转换为项目内部格式
  return parsedData.map((item) => ({
    date: item.date,
    netAssetValue: item.netAssetValue,
    accumulatedValue: item.accumulatedValue,
    dailyGrowthRate: 0, // 计算日增长率或从其他字段获取
  }));
}

/**
 * 通过Next.js API路由获取数据（避免CORS问题）
 */
async function fetchViaApiRoute(
  fundCode: string,
  startDate?: string,
  endDate?: string,
  options?: Required<DataFetchOptions>
): Promise<FundData[]> {
  const parameters = new URLSearchParameters({ code: fundCode });
  if (startDate) parameters.set("startDate", startDate);
  if (endDate) parameters.set("endDate", endDate);

  const fetchWithRetry = () =>
    withRetry(
      async () => {
        const response = await fetch(`/api/fund?${parameters.toString()}`, {
          method: "GET",
          headers: {
            Accept: "application/json, text/plain, */*",
            "Cache-Control": "no-cache",
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
          throw new Error(`API错误: ${result.error}`);
        }

        return result.data as FundData[];
      },
      {
        maxRetries: options?.retryCount || 3,
        delay: options?.retryDelay || 1000,
      }
    );

  return withTimeout(fetchWithRetry(), options?.timeout || 10_000);
}

/**
 * 按日期范围过滤数据
 */
function filterByDateRange(
  data: FundData[],
  startDate?: string,
  endDate?: string
): FundData[] {
  return data.filter((item) => {
    const itemDate = new Date(item.date);

    // Simplified return statement to fix sonarjs/prefer-single-boolean-return
    return !(
      (startDate && itemDate < new Date(startDate)) ||
      (endDate && itemDate > new Date(endDate))
    );
  });
}

// 便捷函数别名
export async function fetchFundData(
  fundCode: string,
  startDate?: string,
  endDate?: string,
  options?: DataFetchOptions
): Promise<FundData[]> {
  return getFundData(fundCode, startDate, endDate, options);
}

export { validateFundCode as validateFund };
export { getFundInfo as getFundBasicInfo };

import { describe, it, expect, vi, beforeEach } from "vitest";

import { createBacktestEngine, runBacktest } from "@/lib/backtest";
import type {
  FundData,
  IndexData,
  Fund,
  FixedAmountParameters,
  GridTradingParameters,
} from "@/types/fund";

// Mock data
const mockFundData: FundData[] = [
  { date: "2024-01-01", netAssetValue: 1, accumulatedValue: 1 },
  { date: "2024-01-02", netAssetValue: 1.01, accumulatedValue: 1.01 },
  { date: "2024-01-03", netAssetValue: 0.995, accumulatedValue: 0.995 },
  { date: "2024-01-04", netAssetValue: 1.02, accumulatedValue: 1.02 },
  { date: "2024-01-05", netAssetValue: 1.015, accumulatedValue: 1.015 },
];

const mockIndexData: IndexData[] = [
  { date: "2024-01-01", value: 3000, change: 0 },
  { date: "2024-01-02", value: 3030, change: 1 },
  { date: "2024-01-03", value: 2985, change: -1.5 },
  { date: "2024-01-04", value: 3060, change: 2.5 },
  { date: "2024-01-05", value: 3045, change: -0.5 },
];

const mockFund: Fund = {
  id: "test_fund",
  name: "测试基金",
  code: "000001",
  type: "stock",
  indexId: "test_index",
};

describe("Backtest Functions", () => {
  let engineConfig: { fundData: FundData[]; indexData?: IndexData[] };

  beforeEach(() => {
    engineConfig = createBacktestEngine(mockFundData, mockIndexData);
  });

  describe("createBacktestEngine", () => {
    it("应该正确创建回测引擎配置", () => {
      expect(engineConfig).toBeDefined();
      expect(engineConfig.fundData).toBeDefined();
      expect(engineConfig.indexData).toBeDefined();
    });

    it("应该对数据按日期排序", () => {
      const unsortedData: FundData[] = [
        { date: "2024-01-03", netAssetValue: 0.995, accumulatedValue: 0.995 },
        { date: "2024-01-01", netAssetValue: 1, accumulatedValue: 1 },
        { date: "2024-01-02", netAssetValue: 1.01, accumulatedValue: 1.01 },
      ];

      const testConfig = createBacktestEngine(unsortedData);
      expect(testConfig.fundData[0].date).toBe("2024-01-01");
      expect(testConfig.fundData[1].date).toBe("2024-01-02");
      expect(testConfig.fundData[2].date).toBe("2024-01-03");
    });
  });

  describe("runBacktest", () => {
    it("应该执行定投策略回测", async () => {
      const parameters: FixedAmountParameters = {
        startDate: "2024-01-01",
        endDate: "2024-01-05",
        initialAmount: 1000,
        monthlyAmount: 500,
        frequency: "monthly",
      };

      const result = await runBacktest(engineConfig, mockFund, parameters);

      expect(result).toBeDefined();
      expect(result.strategy).toBe("fixed_amount");
      expect(result.fund).toEqual(mockFund);
      expect(result.params).toEqual(parameters);
      expect(result.performance).toBeDefined();
      expect(result.timeline).toBeDefined();
      expect(Array.isArray(result.timeline)).toBe(true);
    });

    it("应该执行网格交易策略回测", async () => {
      const parameters: GridTradingParameters = {
        startDate: "2024-01-01",
        endDate: "2024-01-05",
        initialAmount: 10_000,
        gridCount: 5,
        priceRange: { min: 0.95, max: 1.05 },
        investmentPerGrid: 2000,
        rebalanceFrequency: "daily",
      };

      const result = await runBacktest(engineConfig, mockFund, parameters);

      expect(result).toBeDefined();
      expect(result.strategy).toBe("grid_trading");
      expect(result.timeline.length).toBeGreaterThan(0);
    });
  });



  describe("边界情况处理", () => {
    it("应该处理空的基金数据", () => {
      const emptyConfig = createBacktestEngine([]);
      expect(emptyConfig).toBeDefined();
      expect(emptyConfig.fundData).toEqual([]);
    });

    it("应该处理单日数据", () => {
      const singleDayData: FundData[] = [
        { date: "2024-01-01", netAssetValue: 1, accumulatedValue: 1 },
      ];

      const singleDayConfig = createBacktestEngine(singleDayData);
      expect(singleDayConfig).toBeDefined();
      expect(singleDayConfig.fundData.length).toBe(1);
    });

    it("应该处理无效的日期范围", async () => {
      const parameters: FixedAmountParameters = {
        startDate: "2024-12-31", // 超出数据范围
        endDate: "2024-12-31",
        initialAmount: 1000,
        monthlyAmount: 500,
        frequency: "monthly",
      };

      const result = await runBacktest(engineConfig, mockFund, parameters);
      expect(result.timeline.length).toBe(0);
    });
  });
});
